{"name": "dental_web_app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "start": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@tailwindcss/postcss": "^4.1.11", "@tailwindcss/vite": "^4.1.8", "@vitejs/plugin-react-swc": "^3.11.0", "apexcharts": "^4.7.0", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "core-js": "^3.44.0", "css-vars-ponyfill": "^2.4.9", "dayjs": "^1.11.13", "lucide-react": "^0.513.0", "react": "^19.1.0", "react-apexcharts": "^1.7.0", "react-dom": "^19.1.0", "react-form-stepper": "^2.0.3", "react-hook-form": "^7.57.0", "react-router-dom": "^7.6.1", "react-slick": "^0.30.3", "react-toastify": "^11.0.5", "recharts": "^2.15.3", "tailwind-merge": "^3.3.0", "vite-plugin-html": "^3.2.2", "vite-plugin-image-optimizer": "^2.0.2", "vite-plugin-pages": "^0.33.1", "vite-plugin-sitemap": "^0.8.2", "vite-ssg": "^28.0.0"}, "devDependencies": {"@eslint/js": "^9.25.0", "@iconify-icon/react": "^3.0.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.6", "tailwindcss": "^4.1.11", "tw-animate-css": "^1.3.4", "vite": "^6.3.5"}}